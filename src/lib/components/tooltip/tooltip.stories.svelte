<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Tooltip from '$lib/components/tooltip/tooltip.svelte';
	import TooltipProvider from '$lib/components/tooltip-provider/tooltip-provider.svelte';
	import { Placement } from '$lib/enums/placement.js';

	const { Story } = defineMeta({
		title: 'Components/Tooltip',
		component: Tooltip,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['content', 'placement', 'showArrow', 'delayDuration']
			},
			layout: 'centered'
		},
		argTypes: {
			content: {
				control: 'text'
			},
			placement: {
				control: 'select',
				options: Object.values(Placement)
			},
			showArrow: {
				control: 'boolean'
			},
			delayDuration: {
				control: 'number'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		content: 'This is a helpful tooltip',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 200
	}}
>
	<TooltipProvider delayDuration={200}>
		<Tooltip content="This is a helpful tooltip" placement={Placement.TOP} showArrow={false}>
			<span
				class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
			>
				Hover me
			</span>
		</Tooltip>
	</TooltipProvider>
</Story>

<Story
	name="With Arrow"
	args={{
		content: 'Tooltip with arrow',
		placement: Placement.BOTTOM,
		showArrow: true,
		delayDuration: 200
	}}
>
	<TooltipProvider delayDuration={200}>
		<Tooltip content="Tooltip with arrow" placement={Placement.BOTTOM} showArrow={true}>
			<span
				class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
			>
				Hover for arrow
			</span>
		</Tooltip>
	</TooltipProvider>
</Story>

<Story
	name="Different Placements"
	args={{
		content: 'Tooltip placement demo',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 200
	}}
>
	<TooltipProvider delayDuration={200}>
		<div class="flex gap-4">
			<Tooltip content="Top tooltip" placement={Placement.TOP}>
				<span
					class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
				>
					Top
				</span>
			</Tooltip>
			<Tooltip content="Right tooltip" placement={Placement.RIGHT}>
				<span
					class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
				>
					Right
				</span>
			</Tooltip>
			<Tooltip content="Bottom tooltip" placement={Placement.BOTTOM}>
				<span
					class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
				>
					Bottom
				</span>
			</Tooltip>
			<Tooltip content="Left tooltip" placement={Placement.LEFT}>
				<span
					class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
				>
					Left
				</span>
			</Tooltip>
		</div>
	</TooltipProvider>
</Story>

<Story
	name="Fast Delay"
	args={{
		content: 'Quick tooltip with no delay',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 0
	}}
>
	<TooltipProvider delayDuration={0}>
		<Tooltip
			content="Quick tooltip with no delay"
			placement={Placement.TOP}
			showArrow={false}
			delayDuration={0}
		>
			<span
				class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
			>
				Instant tooltip
			</span>
		</Tooltip>
	</TooltipProvider>
</Story>
